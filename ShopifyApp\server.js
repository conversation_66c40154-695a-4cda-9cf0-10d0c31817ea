const express = require("express");
const crypto = require("crypto");
const bodyParser = require("body-parser");
const dotenv = require("dotenv");
const {
  shopifyApi,
  BillingInterval,
  LATEST_API_VERSION,
} = require("@shopify/shopify-api");

// Import Node.js adapter for Shopify API v8
require("@shopify/shopify-api/adapters/node");
const jwt = require("jsonwebtoken");
const path = require("path");
const fs = require("fs");
const fetch = require("node-fetch");

// Load environment variables
dotenv.config();

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 3000;

// Define billing plans for Shopify Billing API
const billingPlans = {
  basic: {
    amount: 49.0,
    currencyCode: "USD",
    interval: BillingInterval.Every30Days,
    trialDays: 7,
  },
  pro: {
    amount: 179.0,
    currencyCode: "USD",
    interval: BillingInterval.Every30Days,
    trialDays: 7,
  },
  enterprise: {
    amount: 400.0,
    currencyCode: "USD",
    interval: BillingInterval.Every30Days,
    trialDays: 14,
  },
};

// Initialize Shopify API with proper configuration
const shopify = shopifyApi({
  apiKey: process.env.SHOPIFY_API_KEY || "d36f16d2a6e8767e33a54d83ba172a41",
  apiSecretKey: process.env.SHOPIFY_API_SECRET || "",
  scopes: ["read_products", "read_themes", "write_themes"],
  hostName: process.env.HOST || "morpho.modularcx.io",
  isEmbeddedApp: true,
  apiVersion: LATEST_API_VERSION,
});

// Store the config for later use
const shopifyConfig = {
  apiKey: process.env.SHOPIFY_API_KEY || "d36f16d2a6e8767e33a54d83ba172a41",
  apiSecretKey: process.env.SHOPIFY_API_SECRET || "",
  scopes: ["read_products", "read_themes", "write_themes"],
  hostName: process.env.HOST || "morpho.modularcx.io",
  isEmbeddedApp: true,
  apiVersion: LATEST_API_VERSION,
};

// Configure fetch with proper timeout
const fetchWithTimeout = (url, options = {}) => {
  const timeout = options.timeout || 30000; // 30 seconds default timeout
  return Promise.race([
    fetch(url, {
      ...options,
      timeout: timeout,
      headers: {
        ...options.headers,
        Connection: "keep-alive",
      },
    }),
    new Promise((_, reject) =>
      setTimeout(() => reject(new Error("Request timeout")), timeout)
    ),
  ]);
};

/**
 * Raw body parser middleware - MUST come before any other body parsers
 * This is critical for HMAC verification as per Shopify's requirements
 */
app.use("/morpho/webhooks", express.raw({ type: "application/json" }));

// Regular body parser for other routes
app.use(bodyParser.json());

// Increase payload size limit if needed
app.use(bodyParser.json({ limit: "10mb" }));
app.use(bodyParser.urlencoded({ extended: true, limit: "10mb" }));

// Serve static files from the public directory
app.use(express.static("public"));

// Add CORS and error handling middleware
app.use((req, res, next) => {
  res.header("Access-Control-Allow-Origin", "*");
  res.header("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
  res.header("Access-Control-Allow-Headers", "Content-Type, Authorization");

  if (req.method === "OPTIONS") {
    return res.status(200).end();
  }
  next();
});

// Add keep-alive header to all responses
app.use((req, res, next) => {
  res.set("Connection", "keep-alive");
  next();
});

// Session storage (in production, use a proper session store like Redis)
const sessionStorage = new Map();

// Session token validation middleware with proper Shopify session handling
const validateSessionToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return res.status(401).send("No session token provided");
    }

    const sessionToken = authHeader.replace("Bearer ", "");
    const decoded = jwt.verify(sessionToken, shopifyConfig.apiSecretKey, {
      algorithms: ["HS256"],
      clockTolerance: 60, // 1 minute tolerance for clock skew
    });

    // Validate token fields
    if (
      !decoded.iss ||
      !decoded.dest ||
      !decoded.aud ||
      decoded.aud !== shopifyConfig.apiKey ||
      !decoded.iss.endsWith(".myshopify.com") || // Verify issuer is a Shopify admin domain
      decoded.dest !== decoded.iss.replace("admin.", "")
    ) {
      // Verify dest matches shop domain
      return res.status(401).send("Invalid session token");
    }

    // Get or create session
    const shop = decoded.dest;
    const sessionId = `offline_${shop}`;
    let session = sessionStorage.get(sessionId);

    if (!session) {
      // Create a basic session structure - in production, retrieve from proper session store
      session = {
        id: sessionId,
        shop: shop,
        accessToken: null, // Will be set during OAuth flow
        isOnline: false,
      };
      sessionStorage.set(sessionId, session);
    }

    // Add session info to request
    req.shopifySession = session;
    req.shop = shop;

    next();
  } catch (error) {
    console.error("Session token validation error:", error);
    res.status(401).send("Invalid session token");
  }
};

// Embedded app route
app.get("/embedded-app", (req, res) => {
  console.log("Embedded app route accessed");
  res.sendFile(path.join(__dirname, "public", "embedded-app.html"));
});

// Home route - redirect to Shopify admin
app.get("/", (req, res) => {
  res.sendFile(path.join(__dirname, "public", "index.html"));
});

/**
 * Verify webhook signatures using HMAC
 * Following Shopify's documentation for proper HMAC verification
 */
const verifyShopifyWebhook = (req, res, next) => {
  try {
    const hmacHeader = req.headers["x-shopify-hmac-sha256"];

    // Verify HMAC header exists
    if (!hmacHeader) {
      console.error("HMAC validation failed: missing header");
      return res.status(401).send("HMAC validation failed: missing header");
    }

    // Verify API secret key is configured
    if (!shopifyConfig.apiSecretKey) {
      console.error("HMAC validation failed: missing API secret key");
      return res
        .status(401)
        .send("HMAC validation failed: missing API secret key");
    }

    // Get raw request body for HMAC verification
    const rawBody = req.body; // express.raw provides the raw buffer

    // Generate HMAC signature
    const generatedHash = crypto
      .createHmac("sha256", shopifyConfig.apiSecretKey)
      .update(rawBody) // Use raw buffer directly
      .digest("base64");

    // Compare generated hash with the one provided by Shopify
    if (generatedHash !== hmacHeader) {
      console.error("HMAC validation failed: signature mismatch");
      console.error(`Expected: ${hmacHeader}`);
      console.error(`Generated: ${generatedHash}`);
      return res.status(401).send("HMAC validation failed: signature mismatch");
    }

    // Parse the raw JSON body for subsequent middleware
    req.body = JSON.parse(rawBody);
    next();
  } catch (error) {
    console.error("Webhook verification error:", error);
    return res.status(401).send("HMAC validation failed: verification error");
  }
};

// Function to register webhooks with Shopify
const registerWebhooks = async (shop, accessToken) => {
  try {
    const webhooks = [
      {
        topic: "customers/data_request",
        address: `https://api.modularcx.link/morpho/webhooks`,
      },
      {
        topic: "customers/redact",
        address: `https://api.modularcx.link/morpho/webhooks`,
      },
      {
        topic: "shop/redact",
        address: `https://api.modularcx.link/morpho/webhooks`,
      },
      {
        topic: "app/uninstalled",
        address: `https://api.modularcx.link/morpho/webhooks`,
      },
      {
        topic: "shop/update",
        address: `https://api.modularcx.link/morpho/webhooks`,
      },
    ];

    console.log(`Registering webhooks for ${shop}`);

    // Create session for REST client
    const session = {
      id: `offline_${shop}`,
      shop: shop,
      accessToken: accessToken,
      isOnline: false,
    };

    // Use Shopify REST client
    const client = new shopify.clients.Rest({ session });

    for (const webhook of webhooks) {
      try {
        const response = await client.post({
          path: "webhooks",
          data: {
            webhook: {
              topic: webhook.topic,
              address: webhook.address,
              format: "json",
            },
          },
        });

        console.log(`Registered webhook: ${webhook.topic}`);
      } catch (error) {
        console.error(`Failed to register webhook ${webhook.topic}:`, error);
        // Don't throw for individual webhook failures in development
      }
    }

    console.log("Webhook registration completed");
    return true;
  } catch (error) {
    console.error("Error in webhook registration:", error);
    return false;
  }
};

// Update webhook endpoint to use fetchWithTimeout
app.post("/morpho/webhooks", verifyShopifyWebhook, async (req, res) => {
  const webhookTopic = req.headers["x-shopify-topic"];

  try {
    // Forward the webhook to your backend API with timeout
    const response = await fetchWithTimeout(
      `https://api.modularcx.link/morpho/webhooks`,
      {
        method: "POST",
        timeout: 30000, // 30 seconds timeout
        headers: {
          "Content-Type": "application/json",
          Connection: "keep-alive",
          "X-Shopify-Shop-Domain": req.headers["x-shopify-shop-domain"],
          "X-Shopify-Hmac-Sha256": req.headers["x-shopify-hmac-sha256"],
          "X-Shopify-Topic": webhookTopic,
          "X-Shopify-Api-Version": req.headers["x-shopify-api-version"],
        },
        body: JSON.stringify(req.body),
      }
    );

    if (!response.ok) {
      throw new Error(`Backend API responded with status: ${response.status}`);
    }

    console.log(`Successfully processed webhook: ${webhookTopic}`);
    res.status(200).send("OK");
  } catch (error) {
    console.error(`Error processing webhook ${webhookTopic}:`, error);
    // Return 200 as HMAC was valid, even if processing failed
    res.status(200).send("Webhook received");
  }
});

// Auth routes - Fixed
app.get("/api/auth", (req, res) => {
  const shop = req.query.shop;
  const host = req.query.host;

  if (!shop) {
    return res.status(400).send("Missing shop parameter");
  }

  // Validate shop domain
  if (!shop.match(/^[a-zA-Z0-9\-]+\.myshopify\.com$/)) {
    return res.status(400).send("Invalid shop domain");
  }

  console.log(`Starting auth flow for shop: ${shop}`);

  // Build the OAuth URL
  const authUrl =
    `https://${shop}/admin/oauth/authorize?` +
    `client_id=${shopifyConfig.apiKey}&` +
    `scope=${shopifyConfig.scopes.join(",")}&` +
    `redirect_uri=https://${shopifyConfig.hostName}/auth/callback&` +
    `state=${host || ""}`;

  console.log(`Redirecting to: ${authUrl}`);
  res.redirect(authUrl);
});

app.get("/auth/callback", async (req, res) => {
  const { shop, code, state } = req.query;

  if (!shop || !code) {
    console.error("Missing required parameters in callback");
    return res.status(400).send("Missing required parameters");
  }

  // Validate shop domain again
  if (!shop.match(/^[a-zA-Z0-9\-]+\.myshopify\.com$/)) {
    return res.status(400).send("Invalid shop domain");
  }

  try {
    console.log(`Processing auth callback for shop: ${shop}`);

    // Exchange code for access token using direct API call (compatible with v8)
    const tokenResponse = await fetchWithTimeout(
      `https://${shop}/admin/oauth/access_token`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          client_id: shopifyConfig.apiKey,
          client_secret: shopifyConfig.apiSecretKey,
          code: code,
        }),
      }
    );

    if (!tokenResponse.ok) {
      throw new Error(`Token exchange failed: ${tokenResponse.status}`);
    }

    const tokenData = await tokenResponse.json();
    const accessToken = tokenData.access_token;

    // Create session object
    const session = {
      id: `offline_${shop}`,
      shop: shop,
      accessToken: accessToken,
      isOnline: false,
      scope: tokenData.scope,
    };

    console.log(`Successfully obtained access token for ${shop}`);

    // Store session in session storage
    sessionStorage.set(session.id, session);

    // Register webhooks
    await registerWebhooks(shop, accessToken);

    // Create the redirect URL for embedded app, including the host parameter
    let redirectUrl = `https://${shop}/admin/apps/${shopifyConfig.apiKey}`;
    if (state) {
      redirectUrl += `?host=${state}`;
    }
    console.log(`Redirecting to app: ${redirectUrl}`);
    res.redirect(redirectUrl);
  } catch (error) {
    console.error("Auth callback error:", error);
    res.status(500).send(`Authentication error: ${error.message}`);
  }
});

// Catch-all route for unhandled webhook topics
app.post("/morpho/webhooks/*", (req, res) => {
  // If we get here, HMAC was valid (passed middleware) but route not found
  res.status(200).send("Webhook received");
});

// Protected API routes (require session token)
app.get("/api/app-data", validateSessionToken, async (req, res) => {
  try {
    // The session is already validated by Shopify's middleware
    // req.shopify.session contains the validated session
    const { shop } = req.shopifySession;

    // Your API logic here
    res.json({
      shop,
      timestamp: new Date().toISOString(),
      message: "App data loaded successfully",
    });
  } catch (error) {
    console.error("Error fetching app data:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

app.get("/api/products", validateSessionToken, async (req, res) => {
  try {
    const { shop } = req.shopifySession;

    // Get products from Shopify
    const session = {
      id: req.shopifySession.id || `offline_${shop}`,
      shop,
      accessToken: req.shopifySession.accessToken,
      isOnline: false,
    };
    const client = new shopify.clients.Rest({ session });
    const response = await client.get({
      path: "products",
    });

    // Get 3D model status from Morpho API
    const morphoResponse = await fetchWithTimeout(
      "https://api.modularcx.link/morpho/products/status",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          shop,
          products: response.body.products.map((p) => ({
            id: p.id,
            title: p.title,
            handle: p.handle,
          })),
        }),
      }
    );

    if (!morphoResponse.ok) {
      throw new Error("Failed to get 3D model status");
    }

    const modelStatus = await morphoResponse.json();

    // Combine Shopify product data with 3D model status
    const products = response.body.products.map((product) => ({
      id: product.id,
      title: product.title,
      image: product.image?.src,
      handle: product.handle,
      has3DModel:
        modelStatus.products.find((p) => p.id === product.id)?.has3DModel ||
        false,
    }));

    res.json({ products });
  } catch (error) {
    console.error("Error fetching products:", error);
    res.status(500).json({ error: "Failed to fetch products" });
  }
});

app.post("/api/products/sync", validateSessionToken, async (req, res) => {
  try {
    const { shop } = req.shopifySession;
    const { products } = req.body;

    // Sync selected products with Morpho
    const response = await fetchWithTimeout(
      "https://api.modularcx.link/morpho/products/sync",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          shop,
          products: products.map((p) => ({
            id: p.id,
            title: p.title,
            handle: p.handle,
          })),
        }),
      }
    );

    if (!response.ok) {
      throw new Error("Failed to sync products with Morpho");
    }

    const result = await response.json();
    res.json(result);
  } catch (error) {
    console.error("Error syncing products:", error);
    res.status(500).json({ error: "Failed to sync products" });
  }
});

app.post("/api/products/sync-all", validateSessionToken, async (req, res) => {
  try {
    const { shop } = req.shopifySession;

    // Get all products from Shopify
    const session = {
      id: req.shopifySession.id || `offline_${shop}`,
      shop,
      accessToken: req.shopifySession.accessToken,
      isOnline: false,
    };
    const client = new shopify.clients.Rest({ session });
    const response = await client.get({
      path: "products",
    });

    // Sync all products with Morpho
    const morphoResponse = await fetchWithTimeout(
      "https://api.modularcx.link/morpho/products/sync-all",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          shop,
          products: response.body.products.map((p) => ({
            id: p.id,
            title: p.title,
            handle: p.handle,
          })),
        }),
      }
    );

    if (!morphoResponse.ok) {
      throw new Error("Failed to sync all products with Morpho");
    }

    const result = await morphoResponse.json();
    res.json(result);
  } catch (error) {
    console.error("Error syncing all products:", error);
    res.status(500).json({ error: "Failed to sync all products" });
  }
});

// Billing API routes
app.get("/api/billing/plans", validateSessionToken, async (req, res) => {
  try {
    const plans = [
      {
        id: "basic",
        name: "Basic",
        price: 49,
        interval: "month",
        description: "Perfect for small Shopify stores with a moderate catalog",
        features: [
          "Up to 25 products in 3D",
          "Core features",
          "Free integration with e-commerce platforms",
          "Basic analytics",
          "Standard support",
          "10% off model services",
        ],
      },
      {
        id: "pro",
        name: "Pro",
        price: 179,
        interval: "month",
        description: "Ideal for growing Shopify brands with larger catalogs",
        features: [
          "Up to 150 products in 3D",
          "Full analytics",
          "Priority support",
          "Custom branding",
          "API access",
          "Unlimited model conversions",
          "20% off modeling services",
        ],
      },
      {
        id: "enterprise",
        name: "Enterprise",
        price: 400,
        interval: "month",
        description: "Custom solution for large Shopify Plus merchants",
        features: [
          "Unlimited products (1000+)",
          "Dedicated success manager",
          "SLA",
          "API/SDK",
          "White-label",
          "Full analytics",
          "Custom encryption protocols",
        ],
      },
    ];

    res.json({ plans });
  } catch (error) {
    console.error("Error fetching billing plans:", error);
    res.status(500).json({ error: "Failed to fetch billing plans" });
  }
});

app.post("/api/billing/subscribe", validateSessionToken, async (req, res) => {
  try {
    const { planId } = req.body;
    const session = req.shopifySession;

    if (!session.accessToken) {
      return res.status(401).json({ error: "No access token available" });
    }

    // Check if plan exists
    const plan = billingPlans[planId];
    if (!plan) {
      return res.status(400).json({ error: "Invalid plan ID" });
    }

    // Initialize billing with Shopify API
    const billing = shopify.billing;

    // Request billing subscription
    const billingResponse = await billing.request({
      session,
      plan: planId,
      isTest: process.env.NODE_ENV !== "production",
      returnUrl: `https://${shopifyConfig.hostName}/billing/callback`,
      returnObject: true,
    });

    res.json({
      confirmationUrl: billingResponse.confirmationUrl,
      subscription: billingResponse.appSubscription,
    });
  } catch (error) {
    console.error("Error creating subscription:", error);
    res.status(500).json({ error: "Failed to create subscription" });
  }
});

app.get("/api/billing/status", validateSessionToken, async (req, res) => {
  try {
    const session = req.shopifySession;

    if (!session.accessToken) {
      return res.status(401).json({ error: "No access token available" });
    }

    // Initialize billing with Shopify API
    const billing = shopify.billing;

    // Check current billing status
    const billingCheck = await billing.check({
      session,
      plans: Object.keys(billingPlans),
      isTest: process.env.NODE_ENV !== "production",
      returnObject: true,
    });

    res.json({
      hasActivePayment: billingCheck.hasActivePayment,
      subscriptions: billingCheck.appSubscriptions || [],
    });
  } catch (error) {
    console.error("Error checking billing status:", error);
    res.status(500).json({ error: "Failed to check billing status" });
  }
});

app.get("/billing/callback", async (req, res) => {
  try {
    const { shop, charge_id } = req.query;

    if (!shop || !charge_id) {
      return res.status(400).send("Missing required parameters");
    }

    console.log(`Billing callback for shop: ${shop}, charge: ${charge_id}`);

    // Redirect back to the app
    const redirectUrl = `https://${shop}/admin/apps/${shopifyConfig.apiKey}`;
    res.redirect(redirectUrl);
  } catch (error) {
    console.error("Billing callback error:", error);
    res.status(500).send("Billing callback error");
  }
});

// Webhook handlers
app.post(
  "/webhooks/app/uninstalled",
  verifyShopifyWebhook,
  async (req, res) => {
    try {
      const shop = req.headers["x-shopify-shop-domain"];
      console.log(`App uninstalled from ${shop}`);

      // Notify Morpho about app uninstallation
      await fetchWithTimeout(
        "https://api.modularcx.link/morpho/webhooks/app-uninstalled",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            shop,
            ...req.body,
          }),
        }
      );

      res.status(200).send("OK");
    } catch (error) {
      console.error("Error handling app/uninstalled webhook:", error);
      res.status(200).send("Webhook received");
    }
  }
);

app.post("/webhooks/shop/update", verifyShopifyWebhook, async (req, res) => {
  try {
    const shop = req.headers["x-shopify-shop-domain"];
    console.log(`Shop updated: ${shop}`);

    // Notify Morpho about shop update
    await fetchWithTimeout(
      "https://api.modularcx.link/morpho/webhooks/shop-update",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          shop,
          ...req.body,
        }),
      }
    );

    res.status(200).send("OK");
  } catch (error) {
    console.error("Error handling shop/update webhook:", error);
    res.status(200).send("Webhook received");
  }
});

app.post("/webhooks/shop/redact", verifyShopifyWebhook, async (req, res) => {
  try {
    const shop = req.headers["x-shopify-shop-domain"];
    console.log(`Shop data redact request received for ${shop}`);

    // Forward to Morpho for GDPR compliance
    await fetchWithTimeout(
      "https://api.modularcx.link/morpho/webhooks/shop-redact",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          shop,
          ...req.body,
        }),
      }
    );

    res.status(200).send("OK");
  } catch (error) {
    console.error("Error handling shop/redact webhook:", error);
    res.status(200).send("Webhook received");
  }
});

app.post(
  "/webhooks/customers/data_request",
  verifyShopifyWebhook,
  async (req, res) => {
    try {
      const shop = req.headers["x-shopify-shop-domain"];
      console.log(`Customer data request received for ${shop}`);

      // Forward to Morpho for GDPR compliance
      await fetchWithTimeout(
        "https://api.modularcx.link/morpho/webhooks/customer-data-request",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            shop,
            ...req.body,
          }),
        }
      );

      res.status(200).send("OK");
    } catch (error) {
      console.error("Error handling customers/data_request webhook:", error);
      res.status(200).send("Webhook received");
    }
  }
);

app.post(
  "/webhooks/customers/redact",
  verifyShopifyWebhook,
  async (req, res) => {
    try {
      const shop = req.headers["x-shopify-shop-domain"];
      console.log(`Customer data redact request received for ${shop}`);

      // Forward to Morpho for GDPR compliance
      await fetchWithTimeout(
        "https://api.modularcx.link/morpho/webhooks/customer-redact",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            shop,
            ...req.body,
          }),
        }
      );

      res.status(200).send("OK");
    } catch (error) {
      console.error("Error handling customers/redact webhook:", error);
      res.status(200).send("Webhook received");
    }
  }
);

// Error handling middleware
app.use((err, req, res, next) => {
  console.error("Server error:", err);
  res.status(500).json({
    error: "An unexpected error occurred",
    message: err.message,
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).send("Not found");
});

/**
 * TLS Certificate Configuration
 *
 * For Shopify apps deployed through the Shopify CLI, TLS certificates are handled automatically.
 * Shopify provides and manages the TLS certificates for your app's domain.
 *
 * This ensures your app meets Shopify's security requirements for:
 * - Valid TLS certificate from a trusted CA
 * - Proper HTTPS implementation
 * - Secure communication between Shopify and your app
 *
 * No additional configuration is needed for TLS when deploying through Shopify's infrastructure.
 */

// Create server with proper timeout
const server = app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`App URL: https://${shopifyConfig.hostName}`);
});

// Configure server timeouts
server.timeout = 120000; // 2 minutes
server.keepAliveTimeout = 60000; // 1 minute
server.headersTimeout = 65000; // Just above keepAliveTimeout as recommended
